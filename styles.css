/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  background: #ffffff;
}

/* Preloader Styles */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  transition: opacity 0.8s ease, visibility 0.8s ease;
}

.preloader.fade-out {
  opacity: 0;
  visibility: hidden;
}

.preloader-content {
  text-align: center;
  color: white;
}

.logo-animation h1 {
  font-family: "Playfair Display", serif;
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 2rem;
  letter-spacing: 0.2em;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.loading-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 2px;
  animation: loadingProgress 2s ease-in-out;
  background-size: 200% 100%;
  animation: loadingProgress 2s ease-in-out, shimmer 1.5s ease-in-out infinite;
}

@keyframes logoGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
  100% {
    text-shadow: 0 0 40px rgba(255, 215, 0, 0.8),
      0 0 60px rgba(255, 215, 0, 0.6);
  }
}

@keyframes loadingProgress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar {
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo h2 {
  color: #2c5aa0;
  font-weight: 700;
  font-size: 1.5rem;
  margin-bottom: 0.2rem;
  font-family: "Playfair Display", serif;
  letter-spacing: 0.05em;
}

.abn {
  font-size: 0.8rem;
  color: #666;
  font-weight: 400;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #2c5aa0;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #2c5aa0;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-contact .phone-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c5aa0;
  text-decoration: none;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border: 2px solid #2c5aa0;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-contact .phone-link:hover {
  background: #2c5aa0;
  color: white;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: #ffffff;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%),
    radial-gradient(
      circle at 25% 25%,
      rgba(44, 90, 160, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 215, 0, 0.05) 0%,
      transparent 50%
    );
  background-size: 100% 100%, 800px 800px, 600px 600px;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(44,90,160,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  text-align: left;
}

.hero-badge {
  display: inline-block;
  background: rgba(44, 90, 160, 0.1);
  color: #2c5aa0;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(44, 90, 160, 0.2);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #1a202c;
  font-family: "Playfair Display", serif;
}

.hero-title .highlight {
  color: #2c5aa0;
  position: relative;
}

.hero-title .highlight::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  border-radius: 2px;
}

.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #64748b;
  margin-bottom: 2rem;
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #2c5aa0;
  font-family: "Playfair Display", serif;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.hero-features {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2.5rem;
}

.feature-pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 0.75rem 1.25rem;
  border-radius: 25px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #475569;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-pill:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.15);
  border-color: #2c5aa0;
}

.feature-pill i {
  color: #2c5aa0;
  font-size: 1rem;
}

/* Hero Visual Card */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  max-width: 500px;
  width: 100%;
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c5aa0;
  font-family: "Playfair Display", serif;
}

.commitment-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.commitment-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.commitment-item:hover {
  background: #f8fafc;
  transform: translateX(5px);
}

.commitment-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.commitment-icon.quality {
  background: linear-gradient(135deg, #10b981, #059669);
}

.commitment-icon.availability {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.commitment-icon.reliability {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.commitment-icon.affordability {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.commitment-text h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.commitment-text p {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.4;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-top: none;
  border-right: none;
  transform: rotate(-45deg);
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.2rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  border-color: #ffd700;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-primary:hover {
  background: transparent;
  color: #ffd700;
  border-color: #ffd700;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
}

.btn-outline {
  background: transparent;
  color: #2c5aa0;
  border-color: #2c5aa0;
}

.btn-outline:hover {
  background: #2c5aa0;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(44, 90, 160, 0.3);
}

.btn i {
  transition: transform 0.3s ease;
}

.btn:hover i {
  transform: translateX(5px);
}

/* Section Styles */
section {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c5aa0;
  margin-bottom: 1rem;
  font-family: "Playfair Display", serif;
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 2px;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* About Section */
.about {
  background: #f8f9fa;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.about-item h3 {
  color: #2c5aa0;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.about-item p {
  color: #666;
  line-height: 1.8;
}

.about-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card i {
  font-size: 2.5rem;
  color: #2c5aa0;
  margin-bottom: 1rem;
}

.feature-card h4 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.feature-card p {
  color: #666;
  font-size: 0.9rem;
}

/* Services Section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  border: 1px solid #eee;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #667eea, #2c5aa0);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(44, 90, 160, 0.15);
}

.service-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-card i {
  font-size: 2.5rem;
  color: #2c5aa0;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(44, 90, 160, 0.1);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.service-card:hover i {
  background: #2c5aa0;
  color: white;
  transform: scale(1.1);
}

.service-card h3 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.service-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Gallery Section */
.gallery {
  background: #f8f9fa;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  aspect-ratio: 4/3;
  cursor: pointer;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 90, 160, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay i {
  color: white;
  font-size: 2rem;
}

/* Contact Section */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-item i {
  font-size: 1.5rem;
  color: #2c5aa0;
  margin-top: 0.2rem;
}

.contact-item h4 {
  color: #333;
  margin-bottom: 0.5rem;
}

.contact-item p {
  color: #666;
  line-height: 1.6;
}

.contact-item a {
  color: #2c5aa0;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.contact-form {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #667eea, #2c5aa0);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1.2rem;
  border: 2px solid #eee;
  border-radius: 12px;
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  background: white;
  box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
  transform: translateY(-2px);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background: #2c5aa0;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
  color: #ffd700;
}

.footer-section p {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.footer-section i {
  margin-right: 0.5rem;
  color: #ffd700;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 1rem 0;
  }

  .nav-contact {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-content {
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-stats {
    justify-content: center;
    gap: 1.5rem;
  }

  .hero-features {
    justify-content: center;
  }

  .hero-cta {
    justify-content: center;
  }

  .visual-card {
    max-width: 400px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-features {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  section {
    padding: 3rem 0;
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

/* Tablet responsive */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}
